/* Base styles - Mobile First Approach */
.audioPlayer {
  width: 100%;
  margin: 12px 0;
  padding: 0;
  box-sizing: border-box;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.audioTitle {
  font-size: 12px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  text-align: center;
  line-height: 1.4;
  word-wrap: break-word;
}

.audio_section {
  display: flex !important;
  gap: 12px;
  align-items: center;
  width: 100%;
  visibility: visible !important;
}

.audio_player {
  width: 100%;
  height: 50px;
  min-height: 50px;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  background: transparent !important;
  border: none;
  outline: none;
}

/* Variants - Mobile First */
.compact {
  margin: 16px 0;
  display: block !important;
  visibility: visible !important;
}

.compact .audio_player {
  height: 50px;
  min-height: 50px;
  display: block !important;
  visibility: visible !important;
}

.inline {
  margin: 8px 0;
  display: block !important;
  visibility: visible !important;
}

.inline .audio_player {
  height: 50px;
  min-height: 50px;
  display: block !important;
  visibility: visible !important;
}

.inline .audioTitle {
  font-size: 12px;
  margin-bottom: 8px;
}

/* Mobile Devices (up to 768px) */
@media (max-width: 768px) {
  .audioPlayer {
    margin: 16px 0;
    padding: 0;
    display: block !important;
    visibility: visible !important;
    width: 100%;
  }

  .audioTitle {
    font-size: 12px;
    margin-bottom: 8px;
    display: block !important;
    visibility: visible !important;
  }

  .audio_section {
    gap: 12px;
    display: flex !important;
    visibility: visible !important;
    width: 100%;
  }

  .audio_player {
    height: 50px;
    min-height: 50px;
    display: block !important;
    visibility: visible !important;
    width: 100%;
    background: transparent !important;
  }

  .compact,
  .inline {
    display: block !important;
    visibility: visible !important;
  }

  .compact .audio_player,
  .inline .audio_player {
    height: 50px;
    min-height: 50px;
    display: block !important;
    visibility: visible !important;
  }
}

/* Tablet (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .audioPlayer {
    margin: 20px 0;
    padding: 0;
    display: block !important;
    visibility: visible !important;
    width: 100%;
  }

  .audioTitle {
    font-size: 14px;
    margin-bottom: 12px;
    display: block !important;
    visibility: visible !important;
  }

  .audio_section {
    gap: 12px;
    display: flex !important;
    visibility: visible !important;
    width: 100%;
  }

  .audio_player {
    height: 50px;
    min-height: 50px;
    display: block !important;
    visibility: visible !important;
    width: 100%;
    background: transparent !important;
  }

  .compact,
  .inline {
    display: block !important;
    visibility: visible !important;
  }

  .compact .audio_player,
  .inline .audio_player {
    height: 50px;
    min-height: 50px;
    display: block !important;
    visibility: visible !important;
  }
}

/* Desktop (1025px+) */
@media (min-width: 1025px) {
  .audioPlayer {
    margin: 24px 0;
    padding: 0;
    display: block !important;
    visibility: visible !important;
    width: 100%;
  }

  .audioTitle {
    font-size: 14px;
    margin-bottom: 12px;
    display: block !important;
    visibility: visible !important;
  }

  .audio_section {
    gap: 12px;
    display: flex !important;
    visibility: visible !important;
    width: 100%;
  }

  .audio_player {
    height: 50px;
    min-height: 50px;
    display: block !important;
    visibility: visible !important;
    width: 100%;
    background: transparent !important;
  }

  .compact,
  .inline {
    display: block !important;
    visibility: visible !important;
  }

  .compact .audio_player,
  .inline .audio_player {
    height: 50px;
    min-height: 50px;
    display: block !important;
    visibility: visible !important;
  }
}

/* Accessibility and Focus States */
.audio_player:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}
